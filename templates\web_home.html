<!DOCTYPE html>
<html>
<head>
    <title>Hand Gesture Recognition</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            text-align: center;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #333;
            color: white;
            padding: 20px 0;
            margin-bottom: 20px;
        }
        h1 {
            margin: 0;
        }
        .video-container {
            margin: 20px auto;
            border: 3px solid #333;
            border-radius: 10px;
            overflow: hidden;
            display: inline-block;
        }
        .gesture-info {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px auto;
            max-width: 640px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .gesture-cards {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }
        .gesture-card {
            background-color: white;
            border-radius: 10px;
            padding: 15px;
            width: 200px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .gesture-card h3 {
            margin-top: 0;
            color: #333;
        }
        footer {
            background-color: #333;
            color: white;
            padding: 20px 0;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>Hand Gesture Recognition</h1>
            <p>Control your computer with hand gestures</p>
        </div>
    </header>

    <div class="container">
        <div class="video-container">
            <img src="{{ url_for('video_feed') }}" width="640" height="480">
        </div>

        <div class="gesture-info">
            <h2>Current Gesture: <span id="current-gesture">None</span></h2>
            <p>Make a gesture with your hand to control your computer.</p>
        </div>

        <h2>Available Gestures</h2>
        <div class="gesture-cards">
            <div class="gesture-card">
                <h3>Thumb Up</h3>
                <p>Extend your thumb upward while keeping all other fingers closed.</p>
            </div>
            <div class="gesture-card">
                <h3>Thumb Down</h3>
                <p>Extend your thumb downward while keeping all other fingers closed.</p>
            </div>
            <div class="gesture-card">
                <h3>Index Up</h3>
                <p>Extend your index finger upward while keeping all other fingers closed.</p>
            </div>
            <div class="gesture-card">
                <h3>Peace Sign</h3>
                <p>Extend your index and middle fingers while keeping other fingers closed.</p>
            </div>
            <div class="gesture-card">
                <h3>Open Palm</h3>
                <p>Extend all fingers with palm facing forward.</p>
            </div>
            <div class="gesture-card">
                <h3>Fist</h3>
                <p>Close all fingers to form a fist.</p>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>&copy; 2023 Hand Gesture Recognition</p>
        </div>
    </footer>

    <script>
        // Update gesture information every second
        function updateGestureInfo() {
            fetch('/get_gesture')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('current-gesture').textContent = data.gesture;
                });
        }
        
        // Update every second
        setInterval(updateGestureInfo, 1000);
        
        // Initial update
        updateGestureInfo();
    </script>
</body>
</html>
