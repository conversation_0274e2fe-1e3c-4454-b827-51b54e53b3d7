<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Hand Gesture Recognition</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            text-align: center;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .video-container {
            margin: 20px 0;
            border: 3px solid #333;
            border-radius: 10px;
            overflow: hidden;
            display: inline-block;
        }
        .stats {
            background-color: white;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: left;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }
        .stat-box {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Minimal Hand Gesture Recognition</h1>
        
        <div class="video-container">
            <img src="{{ url_for('video_feed') }}" width="640" height="480">
        </div>
        
        <div class="stats">
            <h2>Statistics</h2>
            <div class="stats-grid">
                <div class="stat-box">
                    <div class="stat-value" id="fps">0</div>
                    <div class="stat-label">FPS</div>
                </div>
                <div class="stat-box">
                    <div class="stat-value" id="gestures">0</div>
                    <div class="stat-label">Gestures Detected</div>
                </div>
                <div class="stat-box">
                    <div class="stat-value" id="uptime">0</div>
                    <div class="stat-label">Uptime (seconds)</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Update statistics every second
        function updateStats() {
            fetch('/stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('fps').textContent = data.fps.toFixed(1);
                    document.getElementById('gestures').textContent = data.gestures_detected;
                    document.getElementById('uptime').textContent = Math.round(data.uptime);
                });
        }
        
        // Update stats every second
        setInterval(updateStats, 1000);
        
        // Initial update
        updateStats();
    </script>
</body>
</html>
