<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team Contributions - Hand Gesture Control System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #333;
            color: white;
            padding: 20px 0;
            text-align: center;
        }
        h1 {
            margin: 0;
        }
        .team-section {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .member {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        .member:last-child {
            border-bottom: none;
        }
        .member-info {
            flex: 1;
        }
        .member-photo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 20px;
            background-color: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .member-photo img {
            width: 100%;
            height: auto;
        }
        .member h3 {
            margin-top: 0;
            color: #333;
        }
        .contribution-list {
            list-style-type: disc;
            padding-left: 20px;
        }
        .nav-buttons {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        .btn {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 0 10px;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #45a049;
        }
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>Team Contributions</h1>
            <p>Meet the team behind the Hand Gesture Control System</p>
        </div>
    </header>

    <div class="container">
        <div class="nav-buttons">
            <a href="/" class="btn">Home</a>
            <a href="/hand_gesture_signs" class="btn">Gesture Guide</a>
            <a href="/trained_signs" class="btn">Trained Signs</a>
        </div>

        <div class="team-section">
            <h2>Our Team</h2>
            
            <div class="member">
                <div class="member-photo">
                    <div style="font-size: 40px;">👨‍💻</div>
                </div>
                <div class="member-info">
                    <h3>Team Member 1</h3>
                    <p><strong>Role:</strong> Project Lead & Computer Vision Specialist</p>
                    <p><strong>Contributions:</strong></p>
                    <ul class="contribution-list">
                        <li>Implemented the core hand detection algorithm using MediaPipe</li>
                        <li>Developed the gesture recognition system</li>
                        <li>Optimized performance for real-time processing</li>
                        <li>Coordinated team efforts and project timeline</li>
                    </ul>
                </div>
            </div>
            
            <div class="member">
                <div class="member-photo">
                    <div style="font-size: 40px;">👩‍💻</div>
                </div>
                <div class="member-info">
                    <h3>Team Member 2</h3>
                    <p><strong>Role:</strong> Machine Learning Engineer</p>
                    <p><strong>Contributions:</strong></p>
                    <ul class="contribution-list">
                        <li>Designed and trained the sign language detection model</li>
                        <li>Implemented data collection and preprocessing pipeline</li>
                        <li>Fine-tuned model for improved accuracy</li>
                        <li>Created the model evaluation framework</li>
                    </ul>
                </div>
            </div>
            
            <div class="member">
                <div class="member-photo">
                    <div style="font-size: 40px;">👨‍💻</div>
                </div>
                <div class="member-info">
                    <h3>Team Member 3</h3>
                    <p><strong>Role:</strong> Full-Stack Developer</p>
                    <p><strong>Contributions:</strong></p>
                    <ul class="contribution-list">
                        <li>Developed the web application interface</li>
                        <li>Implemented the Flask backend</li>
                        <li>Created the real-time video streaming functionality</li>
                        <li>Designed and implemented the API endpoints</li>
                    </ul>
                </div>
            </div>
            
            <div class="member">
                <div class="member-photo">
                    <div style="font-size: 40px;">👩‍💻</div>
                </div>
                <div class="member-info">
                    <h3>Team Member 4</h3>
                    <p><strong>Role:</strong> UX/UI Designer & System Integration</p>
                    <p><strong>Contributions:</strong></p>
                    <ul class="contribution-list">
                        <li>Designed the user interface and experience</li>
                        <li>Implemented system controls (volume, brightness, media)</li>
                        <li>Conducted user testing and gathered feedback</li>
                        <li>Created documentation and user guides</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>&copy; 2023 Hand Gesture Control System</p>
        </div>
    </footer>
</body>
</html>
