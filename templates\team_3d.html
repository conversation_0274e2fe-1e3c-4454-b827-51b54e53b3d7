<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team - AI Hand Gesture Control</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/ai_3d_theme.css') }}">
    <style>
        :root {
            --primary-color: #00ffaa;
            --secondary-color: #6a11cb;
            --dark-color: #121212;
            --light-color: #f0f0f0;
            --accent-color: #ff00aa;
            --gradient-bg: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--dark-color);
            color: var(--light-color);
            overflow-x: hidden;
            min-height: 100vh;
            background: radial-gradient(ellipse at bottom, #1B2735 0%, #090A0F 100%);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        header {
            text-align: center;
            padding: 30px 0;
            position: relative;
        }

        h1 {
            font-family: 'Orbitron', sans-serif;
            font-size: 3rem;
            margin-bottom: 10px;
            background: linear-gradient(to right, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 0 10px rgba(0, 255, 170, 0.3);
            animation: pulse 3s infinite;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #ccc;
            margin-bottom: 20px;
        }

        .team-member-card {
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(0, 255, 170, 0.3);
            box-shadow: 0 0 20px rgba(0, 255, 170, 0.2);
            height: 100%;
            transition: all 0.3s ease;
            transform-style: preserve-3d;
            perspective: 1000px;
        }

        .team-member-card:hover {
            transform: translateY(-10px) rotateX(5deg) rotateY(5deg);
            border-color: var(--primary-color);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4), 0 0 30px rgba(0, 255, 170, 0.3);
        }

        .member-photo {
            width: 120px;
            height: 120px;
            margin: 0 auto 20px;
            border-radius: 50%;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            position: relative;
            overflow: hidden;
            animation: float 6s ease-in-out infinite;
        }

        .member-photo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                rgba(0,255,170,0.3),
                rgba(106,17,203,0.3));
            animation: rotate 10s linear infinite;
        }

        .member-photo::after {
            content: '';
            position: absolute;
            top: 5px;
            left: 5px;
            right: 5px;
            bottom: 5px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 50%;
            z-index: 1;
        }

        .member-photo span {
            position: relative;
            z-index: 2;
        }

        .member-name {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: var(--primary-color);
            text-align: center;
        }

        .member-role {
            font-size: 1rem;
            color: var(--accent-color);
            margin-bottom: 15px;
            text-align: center;
            font-weight: 500;
        }

        .contribution-title {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.1rem;
            margin-bottom: 10px;
            color: var(--light-color);
        }

        .contribution-list {
            list-style-type: none;
            padding-left: 0;
        }

        .contribution-list li {
            position: relative;
            padding-left: 20px;
            margin-bottom: 8px;
            color: #ccc;
        }

        .contribution-list li::before {
            content: '>';
            position: absolute;
            left: 0;
            color: var(--primary-color);
        }

        .nav-buttons {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .btn {
            background: rgba(0, 0, 0, 0.5);
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            padding: 10px 20px;
            border-radius: 30px;
            font-family: 'Orbitron', sans-serif;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background: var(--primary-color);
            color: var(--dark-color);
            box-shadow: 0 0 15px rgba(0, 255, 170, 0.5);
        }

        footer {
            text-align: center;
            padding: 30px 0;
            margin-top: 50px;
            color: #666;
            font-size: 0.9rem;
        }

        /* 3D Animation Effects */
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .holographic-line {
            position: absolute;
            height: 2px;
            background: linear-gradient(to right, transparent, var(--primary-color), transparent);
            width: 100%;
            left: 0;
            animation: scan-line 3s linear infinite;
        }

        @keyframes scan-line {
            0% { top: 0; opacity: 0.5; }
            50% { opacity: 1; }
            100% { top: 100%; opacity: 0.5; }
        }

        /* 3D Card Flip Animation */
        .flip-container {
            perspective: 1000px;
            margin-bottom: 20px;
        }

        .flipper {
            transition: transform 0.8s;
            transform-style: preserve-3d;
            position: relative;
            width: 100%;
            height: 100%;
        }

        .flip-container:hover .flipper {
            transform: rotateY(180deg);
        }

        .front, .back {
            backface-visibility: hidden;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 15px;
            padding: 20px;
        }

        .front {
            z-index: 2;
            transform: rotateY(0deg);
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(0, 255, 170, 0.3);
        }

        .back {
            transform: rotateY(180deg);
            background: rgba(10, 10, 20, 0.8);
            border: 1px solid rgba(255, 0, 170, 0.3);
        }

        /* Particle Animation */
        .particle-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: radial-gradient(circle, rgba(0,255,170,0.8) 0%, rgba(0,255,170,0) 70%);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
            100% { transform: translateY(0) rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="stars" id="stars"></div>
    <div class="particle-container" id="particles"></div>
    <div class="holographic-line"></div>

    <div class="container">
        <header>
            <h1>Team Contributions</h1>
            <p class="subtitle">Meet the team behind the AI Hand Gesture Control System</p>
        </header>

        <div class="nav-buttons">
            <a href="/" class="btn">Home</a>
            <a href="/hand_gesture_signs" class="btn">Gesture Guide</a>
        </div>

        <div class="row g-4">
            <!-- Team Member 1 -->
            <div class="col-md-6 col-lg-3">
                <div class="team-member-card">
                    <div class="member-photo">
                        <span>👨‍💻</span>
                    </div>
                    <h3 class="member-name">Team Member 1</h3>
                    <p class="member-role">Project Lead & AI Developer</p>
                    <h4 class="contribution-title">Contributions:</h4>
                    <ul class="contribution-list">
                        <li>Designed core architecture</li>
                        <li>Implemented hand detection</li>
                        <li>Trained gesture recognition model</li>
                        <li>Optimized performance</li>
                    </ul>
                </div>
            </div>

            <!-- Team Member 2 -->
            <div class="col-md-6 col-lg-3">
                <div class="team-member-card">
                    <div class="member-photo">
                        <span>👩‍💻</span>
                    </div>
                    <h3 class="member-name">Team Member 2</h3>
                    <p class="member-role">Backend Developer</p>
                    <h4 class="contribution-title">Contributions:</h4>
                    <ul class="contribution-list">
                        <li>Developed Flask application</li>
                        <li>Created API endpoints</li>
                        <li>Implemented threading for performance</li>
                        <li>Managed data processing pipeline</li>
                    </ul>
                </div>
            </div>

            <!-- Team Member 3 -->
            <div class="col-md-6 col-lg-3">
                <div class="team-member-card">
                    <div class="member-photo">
                        <span>👨‍🎨</span>
                    </div>
                    <h3 class="member-name">Team Member 3</h3>
                    <p class="member-role">Frontend Developer</p>
                    <h4 class="contribution-title">Contributions:</h4>
                    <ul class="contribution-list">
                        <li>Designed 3D UI interface</li>
                        <li>Created interactive animations</li>
                        <li>Implemented responsive design</li>
                        <li>Developed user experience flow</li>
                    </ul>
                </div>
            </div>

            <!-- Team Member 4 -->
            <div class="col-md-6 col-lg-3">
                <div class="team-member-card">
                    <div class="member-photo">
                        <span>👩‍🔬</span>
                    </div>
                    <h3 class="member-name">Team Member 4</h3>
                    <p class="member-role">UX/UI Designer & Tester</p>
                    <h4 class="contribution-title">Contributions:</h4>
                    <ul class="contribution-list">
                        <li>Conducted user testing</li>
                        <li>Refined gesture interactions</li>
                        <li>Created documentation</li>
                        <li>Designed visual elements</li>
                    </ul>
                </div>
            </div>
        </div>

        <footer>
            <p>&copy; 2025 AI Hand Gesture Control System | Advanced Computer Vision Technology</p>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/ai_3d_effects.js') }}"></script>
    <script>
        // Create stars background
        document.addEventListener('DOMContentLoaded', function() {
            // Create stars
            const stars = document.getElementById('stars');
            const count = 200;

            for (let i = 0; i < count; i++) {
                const star = document.createElement('div');
                star.className = 'star';

                // Random position
                const x = Math.random() * 100;
                const y = Math.random() * 100;

                // Random size
                const size = Math.random() * 3;

                // Random animation delay
                const delay = Math.random() * 5;

                star.style.left = `${x}%`;
                star.style.top = `${y}%`;
                star.style.width = `${size}px`;
                star.style.height = `${size}px`;
                star.style.animationDelay = `${delay}s`;

                stars.appendChild(star);
            }

            // Create particles
            const particles = document.getElementById('particles');
            const particleCount = 30;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size between 5px and 20px
                const size = Math.random() * 15 + 5;

                // Random position
                const posX = Math.random() * 100;
                const posY = Math.random() * 100;

                // Random opacity
                const opacity = Math.random() * 0.5 + 0.1;

                // Set styles
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                particle.style.left = `${posX}%`;
                particle.style.top = `${posY}%`;
                particle.style.opacity = opacity;

                // Add animation with random duration and delay
                const duration = Math.random() * 10 + 10; // 10-20s
                const delay = Math.random() * 5; // 0-5s

                particle.style.animationDuration = `${duration}s`;
                particle.style.animationDelay = `${delay}s`;

                particles.appendChild(particle);
            }

            // Add multiple holographic scan lines
            const container = document.querySelector('.container');
            for (let i = 0; i < 3; i++) {
                const line = document.createElement('div');
                line.className = 'holographic-line';
                line.style.animationDelay = `${i * 1}s`;
                document.body.appendChild(line);
            }

            // Add parallax effect to team member cards
            document.addEventListener('mousemove', function(e) {
                const mouseX = e.clientX / window.innerWidth;
                const mouseY = e.clientY / window.innerHeight;

                const offsetX = (mouseX - 0.5) * 20;
                const offsetY = (mouseY - 0.5) * 20;

                const cards = document.querySelectorAll('.team-member-card');
                cards.forEach((card, index) => {
                    const intensity = 0.2 + (index * 0.05);
                    card.style.transform = `translateX(${offsetX * intensity}px) translateY(${offsetY * intensity}px) rotateX(${-offsetY}deg) rotateY(${offsetX}deg)`;
                });
            });
        });
    </script>
</body>
</html>
