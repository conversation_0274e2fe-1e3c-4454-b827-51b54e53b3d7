<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hand Gesture Control System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #333;
            color: white;
            padding: 20px 0;
            text-align: center;
        }
        h1 {
            margin: 0;
        }
        .video-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        .video-feed {
            border: 3px solid #333;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .controls {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        .btn {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 0 10px;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .info-panel {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .gesture-info {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
        }
        .gesture-card {
            width: 30%;
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .gesture-card h3 {
            margin-top: 0;
            color: #333;
        }
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>Enhanced Hand Gesture Control System</h1>
            <p>Control your computer with hand gestures</p>
        </div>
    </header>

    <div class="container">
        <div class="video-container">
            <div class="video-feed">
                <img src="{{ url_for('video_feed') }}" width="640" height="480">
            </div>
        </div>

        <div class="controls">
            <button class="btn" id="gestureMode">Gesture Mode</button>
            <button class="btn" id="signMode">Sign Language Mode</button>
            <button class="btn" id="captureFrame">Capture Frame</button>
        </div>

        <div class="info-panel">
            <h2>Current Detection</h2>
            <div id="currentGesture">No gesture detected</div>
            <div id="confidenceBar" style="width: 100%; height: 20px; background-color: #ddd; margin-top: 10px;">
                <div id="confidenceFill" style="width: 0%; height: 100%; background-color: #4CAF50;"></div>
            </div>
            <div id="confidenceText" style="text-align: right;">0%</div>
        </div>

        <div class="info-panel">
            <h2>Gesture Guide</h2>
            <div class="gesture-info">
                <div class="gesture-card">
                    <h3>Cursor Movement</h3>
                    <p>Extend your index finger and move your hand to control the cursor.</p>
                </div>
                <div class="gesture-card">
                    <h3>Left Click</h3>
                    <p>Touch your thumb to your index finger while keeping other fingers extended.</p>
                </div>
                <div class="gesture-card">
                    <h3>Right Click</h3>
                    <p>Touch your thumb to your middle finger while keeping other fingers extended.</p>
                </div>
                <div class="gesture-card">
                    <h3>Double Click</h3>
                    <p>Touch your thumb to your ring finger while keeping other fingers extended.</p>
                </div>
                <div class="gesture-card">
                    <h3>Drag</h3>
                    <p>Make a fist (close all fingers) and move your hand to drag objects.</p>
                </div>
                <div class="gesture-card">
                    <h3>Scroll</h3>
                    <p>For scroll up: Keep little finger up, all others down.<br>
                       For scroll down: Keep index finger up, all others down.</p>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>&copy; 2023 Hand Gesture Control System</p>
        </div>
    </footer>

    <script>
        // Update current gesture information
        function updateGestureInfo() {
            fetch('/api/current_gesture')
                .then(response => response.json())
                .then(data => {
                    if (data.gesture) {
                        document.getElementById('currentGesture').textContent = `Detected: ${data.gesture}`;
                        document.getElementById('confidenceFill').style.width = `${data.confidence * 100}%`;
                        document.getElementById('confidenceText').textContent = `${(data.confidence * 100).toFixed(1)}%`;
                    } else {
                        document.getElementById('currentGesture').textContent = 'No gesture detected';
                        document.getElementById('confidenceFill').style.width = '0%';
                        document.getElementById('confidenceText').textContent = '0%';
                    }
                });
        }

        // Set detection mode
        function setMode(mode) {
            fetch('/api/mode', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mode: mode }),
            })
                .then(response => response.json())
                .then(data => {
                    console.log('Mode set to:', data.mode);
                });
        }

        // Capture frame
        document.getElementById('captureFrame').addEventListener('click', function() {
            fetch('/capture_frame')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Frame captured successfully!');
                    } else {
                        alert('Failed to capture frame: ' + data.error);
                    }
                });
        });

        // Set mode buttons
        document.getElementById('gestureMode').addEventListener('click', function() {
            setMode('gesture');
            this.style.backgroundColor = '#45a049';
            document.getElementById('signMode').style.backgroundColor = '#4CAF50';
        });

        document.getElementById('signMode').addEventListener('click', function() {
            setMode('sign');
            this.style.backgroundColor = '#45a049';
            document.getElementById('gestureMode').style.backgroundColor = '#4CAF50';
        });

        // Update gesture info every second
        setInterval(updateGestureInfo, 1000);
    </script>
</body>
</html>
