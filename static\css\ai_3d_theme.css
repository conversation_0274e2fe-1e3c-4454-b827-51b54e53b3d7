/* AI 3D Theme CSS - Advanced animations and effects */

/* 3D Particle Animation */
.particle-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
}

.particle {
    position: absolute;
    display: block;
    pointer-events: none;
    background: radial-gradient(circle, rgba(0,255,170,0.8) 0%, rgba(0,255,170,0) 70%);
    border-radius: 50%;
    transform: translateZ(0);
}

/* 3D Floating Elements */
.float-3d {
    transform-style: preserve-3d;
    perspective: 1000px;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px) translateZ(0) rotateX(0deg) rotateY(0deg);
    }
    50% {
        transform: translateY(-20px) translateZ(20px) rotateX(5deg) rotateY(10deg);
    }
    100% {
        transform: translateY(0px) translateZ(0) rotateX(0deg) rotateY(0deg);
    }
}

/* 3D Button Effects */
.btn-3d {
    position: relative;
    background: linear-gradient(145deg, rgba(0,255,170,0.2), rgba(0,255,170,0.1));
    border: 1px solid rgba(0,255,170,0.3);
    color: #00ffaa;
    padding: 12px 24px;
    border-radius: 30px;
    font-family: 'Orbitron', sans-serif;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2), 
                0 0 5px rgba(0,255,170,0.3), 
                inset 0 0 10px rgba(0,255,170,0.1);
    transform-style: preserve-3d;
    perspective: 1000px;
}

.btn-3d:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255,255,255,0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.btn-3d:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 20px rgba(0,0,0,0.3), 
                0 0 10px rgba(0,255,170,0.5), 
                inset 0 0 15px rgba(0,255,170,0.2);
}

.btn-3d:hover:before {
    transform: translateX(100%);
}

.btn-3d:active {
    transform: translateY(1px) scale(0.98);
    box-shadow: 0 2px 10px rgba(0,0,0,0.2), 
                0 0 5px rgba(0,255,170,0.3), 
                inset 0 0 5px rgba(0,255,170,0.1);
}

/* 3D Card Effects */
.card-3d {
    background: rgba(10,10,20,0.7);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(0,255,170,0.2);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3), 
                0 0 10px rgba(0,255,170,0.2);
    transition: all 0.5s ease;
    transform-style: preserve-3d;
    perspective: 1000px;
}

.card-3d:hover {
    transform: translateY(-10px) rotateX(5deg) rotateY(5deg);
    border-color: rgba(0,255,170,0.5);
    box-shadow: 0 15px 40px rgba(0,0,0,0.4), 
                0 0 20px rgba(0,255,170,0.3);
}

.card-3d:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
                rgba(0,255,170,0.1) 0%, 
                rgba(0,255,170,0) 50%, 
                rgba(0,255,170,0.1) 100%);
    border-radius: 15px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.card-3d:hover:before {
    opacity: 1;
}

/* 3D Text Effects */
.text-3d {
    font-family: 'Orbitron', sans-serif;
    color: #fff;
    text-shadow: 0 0 5px rgba(0,255,170,0.5),
                 0 0 10px rgba(0,255,170,0.3),
                 0 0 15px rgba(0,255,170,0.1);
    transition: all 0.3s ease;
}

.text-3d:hover {
    text-shadow: 0 0 10px rgba(0,255,170,0.8),
                 0 0 20px rgba(0,255,170,0.5),
                 0 0 30px rgba(0,255,170,0.3);
    letter-spacing: 1px;
}

/* 3D Holographic Display */
.hologram {
    position: relative;
    border: 1px solid rgba(0,255,170,0.3);
    border-radius: 10px;
    padding: 20px;
    background: rgba(0,0,0,0.3);
    overflow: hidden;
}

.hologram:before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        to bottom right,
        transparent, 
        rgba(0,255,170,0.1), 
        transparent
    );
    transform: rotate(45deg);
    animation: hologram-scan 3s linear infinite;
    pointer-events: none;
}

@keyframes hologram-scan {
    0% {
        transform: translateY(-100%) rotate(45deg);
    }
    100% {
        transform: translateY(100%) rotate(45deg);
    }
}

.hologram:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        0deg,
        rgba(0,255,170,0.03) 0px,
        rgba(0,255,170,0.03) 1px,
        transparent 1px,
        transparent 2px
    );
    pointer-events: none;
}

/* 3D Grid Background */
.grid-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        linear-gradient(rgba(0,255,170,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,255,170,0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    perspective: 1000px;
    transform-style: preserve-3d;
    transform: rotateX(60deg) scale(2);
    z-index: -2;
    opacity: 0.3;
    animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 0 50px;
    }
}

/* 3D Loading Animation */
.loading-3d {
    position: relative;
    width: 100px;
    height: 100px;
    perspective: 1000px;
    transform-style: preserve-3d;
}

.loading-cube {
    position: absolute;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    animation: loading-rotate 5s linear infinite;
}

.loading-face {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0,255,170,0.2);
    border: 1px solid rgba(0,255,170,0.5);
    box-shadow: 0 0 10px rgba(0,255,170,0.3);
    backface-visibility: visible;
}

.loading-face:nth-child(1) { transform: translateZ(50px); }
.loading-face:nth-child(2) { transform: rotateY(180deg) translateZ(50px); }
.loading-face:nth-child(3) { transform: rotateY(90deg) translateZ(50px); }
.loading-face:nth-child(4) { transform: rotateY(-90deg) translateZ(50px); }
.loading-face:nth-child(5) { transform: rotateX(90deg) translateZ(50px); }
.loading-face:nth-child(6) { transform: rotateX(-90deg) translateZ(50px); }

@keyframes loading-rotate {
    0% { transform: rotateX(0deg) rotateY(0deg); }
    100% { transform: rotateX(360deg) rotateY(360deg); }
}

/* 3D Glow Effects */
.glow-3d {
    position: relative;
}

.glow-3d:before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg, 
                rgba(0,255,170,0.5), 
                rgba(106,17,203,0.5), 
                rgba(0,255,170,0.5));
    border-radius: inherit;
    z-index: -1;
    filter: blur(10px);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.glow-3d:hover:before {
    opacity: 1;
}

/* 3D Pulse Animation */
.pulse-3d {
    animation: pulse-3d 2s infinite;
}

@keyframes pulse-3d {
    0% {
        transform: scale(1);
        filter: brightness(1);
    }
    50% {
        transform: scale(1.05);
        filter: brightness(1.2);
    }
    100% {
        transform: scale(1);
        filter: brightness(1);
    }
}

/* 3D Flip Card */
.flip-card-3d {
    perspective: 1000px;
    width: 300px;
    height: 200px;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.8s;
    transform-style: preserve-3d;
}

.flip-card-3d:hover .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 15px;
    padding: 20px;
}

.flip-card-front {
    background: rgba(10,10,20,0.7);
    border: 1px solid rgba(0,255,170,0.3);
}

.flip-card-back {
    background: rgba(10,10,20,0.9);
    border: 1px solid rgba(0,255,170,0.5);
    transform: rotateY(180deg);
}

/* 3D Neon Border */
.neon-border-3d {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
}

.neon-border-3d:before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, 
                #00ffaa, #6a11cb, #2575fc, #00ffaa);
    background-size: 400% 400%;
    z-index: -1;
    border-radius: 17px;
    animation: neon-border-animation 3s ease infinite;
}

@keyframes neon-border-animation {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}
