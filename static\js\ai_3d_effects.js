/**
 * AI 3D Effects - Advanced animations and interactive effects
 * For Hand Gesture Recognition System
 */

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Create particle background
    createParticles();
    
    // Initialize 3D effects
    init3DEffects();
    
    // Add mouse parallax effect
    initParallaxEffect();
    
    // Add 3D hand model animation
    init3DHandModel();
    
    // Initialize holographic UI elements
    initHolographicUI();
});

/**
 * Create floating particles in the background
 */
function createParticles() {
    // Create container if it doesn't exist
    let container = document.querySelector('.particle-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'particle-container';
        document.body.appendChild(container);
    }
    
    // Number of particles
    const particleCount = 50;
    
    // Create particles
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        
        // Random size between 5px and 20px
        const size = Math.random() * 15 + 5;
        
        // Random position
        const posX = Math.random() * 100;
        const posY = Math.random() * 100;
        
        // Random opacity
        const opacity = Math.random() * 0.5 + 0.1;
        
        // Set styles
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        particle.style.left = `${posX}%`;
        particle.style.top = `${posY}%`;
        particle.style.opacity = opacity;
        
        // Add animation with random duration and delay
        const duration = Math.random() * 10 + 10; // 10-20s
        const delay = Math.random() * 5; // 0-5s
        
        particle.style.animation = `float ${duration}s ease-in-out ${delay}s infinite`;
        
        // Add to container
        container.appendChild(particle);
        
        // Add movement animation
        animateParticle(particle);
    }
}

/**
 * Animate a single particle with random movement
 */
function animateParticle(particle) {
    // Initial position
    let posX = parseFloat(particle.style.left);
    let posY = parseFloat(particle.style.top);
    
    // Random movement speed
    const speedX = (Math.random() - 0.5) * 0.1;
    const speedY = (Math.random() - 0.5) * 0.1;
    
    // Animation function
    function move() {
        // Update position
        posX += speedX;
        posY += speedY;
        
        // Boundary check
        if (posX < 0) posX = 100;
        if (posX > 100) posX = 0;
        if (posY < 0) posY = 100;
        if (posY > 100) posY = 0;
        
        // Apply new position
        particle.style.left = `${posX}%`;
        particle.style.top = `${posY}%`;
        
        // Continue animation
        requestAnimationFrame(move);
    }
    
    // Start animation
    move();
}

/**
 * Initialize 3D effects for various elements
 */
function init3DEffects() {
    // Add 3D effect classes to elements
    
    // Buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(btn => {
        btn.classList.add('btn-3d');
    });
    
    // Cards
    const cards = document.querySelectorAll('.gesture-card');
    cards.forEach(card => {
        card.classList.add('card-3d');
    });
    
    // Headings
    const headings = document.querySelectorAll('h1, h2, h3');
    headings.forEach(heading => {
        heading.classList.add('text-3d');
    });
    
    // Gesture info container
    const gestureInfo = document.querySelector('.gesture-info');
    if (gestureInfo) {
        gestureInfo.classList.add('hologram');
    }
    
    // Add 3D grid background
    const gridBg = document.createElement('div');
    gridBg.className = 'grid-bg';
    document.body.appendChild(gridBg);
    
    // Add pulse effect to current gesture
    const gestureName = document.getElementById('current-gesture');
    if (gestureName) {
        gestureName.classList.add('pulse-3d');
    }
    
    // Add glow effect to video container
    const videoContainer = document.querySelector('.video-container');
    if (videoContainer) {
        videoContainer.classList.add('glow-3d');
    }
}

/**
 * Initialize parallax effect based on mouse movement
 */
function initParallaxEffect() {
    document.addEventListener('mousemove', function(e) {
        // Get mouse position
        const mouseX = e.clientX / window.innerWidth;
        const mouseY = e.clientY / window.innerHeight;
        
        // Calculate offset (smaller values for subtle effect)
        const offsetX = (mouseX - 0.5) * 20;
        const offsetY = (mouseY - 0.5) * 20;
        
        // Apply parallax to elements
        
        // Heading
        const heading = document.querySelector('h1');
        if (heading) {
            heading.style.transform = `translateX(${offsetX * 0.5}px) translateY(${offsetY * 0.5}px)`;
        }
        
        // Cards with different intensity
        const cards = document.querySelectorAll('.gesture-card');
        cards.forEach((card, index) => {
            const intensity = 0.2 + (index * 0.05);
            card.style.transform = `translateX(${offsetX * intensity}px) translateY(${offsetY * intensity}px) rotateX(${-offsetY}deg) rotateY(${offsetX}deg)`;
        });
        
        // Grid background with opposite movement
        const gridBg = document.querySelector('.grid-bg');
        if (gridBg) {
            gridBg.style.transform = `rotateX(60deg) scale(2) translateX(${-offsetX * 2}px) translateY(${-offsetY * 2}px)`;
        }
        
        // Particles with subtle movement
        const particles = document.querySelectorAll('.particle');
        particles.forEach((particle, index) => {
            const intensity = 0.1 + (index % 5) * 0.02;
            const currentLeft = parseFloat(particle.style.left);
            const currentTop = parseFloat(particle.style.top);
            
            particle.style.left = `${currentLeft + offsetX * intensity * 0.1}%`;
            particle.style.top = `${currentTop + offsetY * intensity * 0.1}%`;
        });
    });
}

/**
 * Initialize 3D hand model with advanced animation
 */
function init3DHandModel() {
    // Get the hand model container
    const handModel = document.querySelector('.hand-model');
    if (!handModel) return;
    
    // Add interactive rotation based on mouse position
    document.addEventListener('mousemove', function(e) {
        // Get mouse position
        const mouseX = (e.clientX / window.innerWidth - 0.5) * 2;
        const mouseY = (e.clientY / window.innerHeight - 0.5) * 2;
        
        // Apply rotation to hand
        const hand = handModel.querySelector('.hand');
        if (hand) {
            hand.style.transform = `rotateY(${mouseX * 30}deg) rotateX(${-mouseY * 30}deg)`;
        }
    });
    
    // Add finger animation based on current gesture
    setInterval(function() {
        // Get current gesture
        const gestureName = document.getElementById('current-gesture');
        if (!gestureName) return;
        
        const gesture = gestureName.textContent;
        const fingers = handModel.querySelectorAll('.finger');
        const palm = handModel.querySelector('.palm');
        
        // Animate fingers based on gesture
        if (gesture === "Thumb Up") {
            // Thumb up animation
            fingers.forEach((finger, index) => {
                if (index === 0) { // Thumb
                    finger.style.transform = 'translateX(40px) translateY(-10px) translateZ(10px) rotateZ(-30deg)';
                } else {
                    finger.style.transform = `translateX(${20 - index * 20}px) translateY(${10}px) translateZ(10px) rotateX(90deg)`;
                }
            });
        } else if (gesture === "Peace Sign") {
            // Peace sign animation
            fingers.forEach((finger, index) => {
                if (index === 1 || index === 2) { // Index and middle
                    finger.style.transform = `translateX(${20 - index * 20}px) translateY(-15px) translateZ(10px)`;
                } else {
                    finger.style.transform = `translateX(${20 - index * 20}px) translateY(${10}px) translateZ(10px) rotateX(90deg)`;
                }
            });
        } else if (gesture === "Open Palm") {
            // Open palm animation
            fingers.forEach((finger, index) => {
                finger.style.transform = `translateX(${20 - index * 20}px) translateY(-15px) translateZ(10px)`;
            });
        } else if (gesture === "Fist") {
            // Fist animation
            fingers.forEach((finger, index) => {
                finger.style.transform = `translateX(${20 - index * 20}px) translateY(${10}px) translateZ(10px) rotateX(90deg)`;
            });
        } else if (gesture === "OK Sign") {
            // OK sign animation
            fingers.forEach((finger, index) => {
                if (index === 0 || index === 1) { // Thumb and index
                    finger.style.transform = `translateX(${40 - index * 40}px) translateY(${0}px) translateZ(10px) rotateZ(${index === 0 ? -45 : 45}deg)`;
                } else {
                    finger.style.transform = `translateX(${20 - index * 20}px) translateY(-15px) translateZ(10px)`;
                }
            });
        }
    }, 1000);
}

/**
 * Initialize holographic UI elements with scan lines and glitch effects
 */
function initHolographicUI() {
    // Create scan line effect
    const scanLine = document.createElement('div');
    scanLine.className = 'scan-line';
    scanLine.style.position = 'fixed';
    scanLine.style.top = '0';
    scanLine.style.left = '0';
    scanLine.style.width = '100%';
    scanLine.style.height = '2px';
    scanLine.style.background = 'linear-gradient(to right, transparent, rgba(0,255,170,0.5), transparent)';
    scanLine.style.zIndex = '9999';
    scanLine.style.pointerEvents = 'none';
    document.body.appendChild(scanLine);
    
    // Animate scan line
    let scanPos = 0;
    setInterval(function() {
        scanPos = (scanPos + 1) % window.innerHeight;
        scanLine.style.top = `${scanPos}px`;
    }, 20);
    
    // Add occasional glitch effect
    setInterval(function() {
        if (Math.random() > 0.95) { // 5% chance of glitch
            // Apply glitch effect to UI elements
            const elements = document.querySelectorAll('.gesture-name, .gesture-title, h1');
            
            elements.forEach(el => {
                // Save original text
                const originalText = el.textContent;
                
                // Create glitched text
                const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?/';
                let glitchedText = '';
                for (let i = 0; i < originalText.length; i++) {
                    if (Math.random() > 0.7) {
                        glitchedText += glitchChars[Math.floor(Math.random() * glitchChars.length)];
                    } else {
                        glitchedText += originalText[i];
                    }
                }
                
                // Apply glitched text
                el.textContent = glitchedText;
                
                // Add visual glitch effect
                el.style.textShadow = '2px 0 #ff00aa, -2px 0 #00ffaa';
                el.style.transform = `translateX(${(Math.random() - 0.5) * 10}px)`;
                
                // Reset after short delay
                setTimeout(function() {
                    el.textContent = originalText;
                    el.style.textShadow = '';
                    el.style.transform = '';
                }, 200);
            });
        }
    }, 3000);
    
    // Add data visualization animation to confidence bar
    const confidenceFill = document.getElementById('confidence-fill');
    if (confidenceFill) {
        // Add data particles
        for (let i = 0; i < 10; i++) {
            const particle = document.createElement('div');
            particle.className = 'data-particle';
            particle.style.position = 'absolute';
            particle.style.width = '3px';
            particle.style.height = '3px';
            particle.style.background = '#fff';
            particle.style.borderRadius = '50%';
            particle.style.top = `${Math.random() * 100}%`;
            particle.style.left = `${Math.random() * 100}%`;
            particle.style.opacity = Math.random() * 0.7 + 0.3;
            
            confidenceFill.appendChild(particle);
            
            // Animate particle
            setInterval(function() {
                particle.style.top = `${Math.random() * 100}%`;
                particle.style.left = `${Math.random() * 100}%`;
            }, 1000 + Math.random() * 2000);
        }
    }
}
