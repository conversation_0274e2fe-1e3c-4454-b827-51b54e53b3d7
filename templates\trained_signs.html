<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trained Signs - Hand Gesture Control System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #333;
            color: white;
            padding: 20px 0;
            text-align: center;
        }
        h1 {
            margin: 0;
        }
        .signs-section {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .signs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .sign-card {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        .sign-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .sign-card h3 {
            margin-top: 0;
            color: #333;
            text-align: center;
        }
        .sign-image {
            width: 100%;
            height: 150px;
            background-color: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            border-radius: 5px;
            overflow: hidden;
        }
        .sign-image img {
            max-width: 100%;
            max-height: 100%;
        }
        .nav-buttons {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        .btn {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 0 10px;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #45a049;
        }
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>Trained Sign Language Signs</h1>
            <p>Learn about the sign language signs recognized by our system</p>
        </div>
    </header>

    <div class="container">
        <div class="nav-buttons">
            <a href="/" class="btn">Home</a>
            <a href="/team" class="btn">Team</a>
            <a href="/hand_gesture_signs" class="btn">Gesture Guide</a>
        </div>

        <div class="signs-section">
            <h2>Recognized Sign Language Signs</h2>
            <p>Our system is trained to recognize the following sign language signs:</p>
            
            <div class="signs-grid">
                <div class="sign-card">
                    <div class="sign-image">
                        <div style="font-size: 60px;">👍</div>
                    </div>
                    <h3>Thumb Up</h3>
                    <p>Extend your thumb upward while keeping all other fingers closed.</p>
                </div>
                
                <div class="sign-card">
                    <div class="sign-image">
                        <div style="font-size: 60px;">👎</div>
                    </div>
                    <h3>Thumb Down</h3>
                    <p>Extend your thumb downward while keeping all other fingers closed.</p>
                </div>
                
                <div class="sign-card">
                    <div class="sign-image">
                        <div style="font-size: 60px;">☝️</div>
                    </div>
                    <h3>Index Up</h3>
                    <p>Extend your index finger upward while keeping all other fingers closed.</p>
                </div>
                
                <div class="sign-card">
                    <div class="sign-image">
                        <div style="font-size: 60px;">🤞</div>
                    </div>
                    <h3>Peace Sign</h3>
                    <p>Extend your index and middle fingers while keeping other fingers closed.</p>
                </div>
                
                <div class="sign-card">
                    <div class="sign-image">
                        <div style="font-size: 60px;">👌</div>
                    </div>
                    <h3>OK Sign</h3>
                    <p>Form a circle with your thumb and index finger, extending other fingers.</p>
                </div>
                
                <div class="sign-card">
                    <div class="sign-image">
                        <div style="font-size: 60px;">✋</div>
                    </div>
                    <h3>Open Hand</h3>
                    <p>Extend all fingers with palm facing forward.</p>
                </div>
                
                <div class="sign-card">
                    <div class="sign-image">
                        <div style="font-size: 60px;">✊</div>
                    </div>
                    <h3>Closed Fist</h3>
                    <p>Close all fingers to form a fist.</p>
                </div>
                
                <div class="sign-card">
                    <div class="sign-image">
                        <div style="font-size: 60px;">👋</div>
                    </div>
                    <h3>Wave</h3>
                    <p>Extend all fingers and move your hand side to side.</p>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>&copy; 2023 Hand Gesture Control System</p>
        </div>
    </footer>
</body>
</html>
