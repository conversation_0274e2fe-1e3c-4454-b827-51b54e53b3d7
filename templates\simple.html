<!DOCTYPE html>
<html>
<head>
    <title>Simple Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            text-align: center;
        }
        h1 {
            color: #333;
        }
        .video-container {
            margin: 20px auto;
            border: 3px solid #333;
            border-radius: 10px;
            overflow: hidden;
            display: inline-block;
        }
    </style>
</head>
<body>
    <h1>Simple Hand Gesture Recognition Test</h1>
    <div class="video-container">
        <img src="{{ url_for('video_feed') }}" width="640" height="480">
    </div>
    <div id="gesture-info">
        <h2>Current Gesture: <span id="current-gesture">None</span></h2>
        <h3>Confidence: <span id="confidence">0%</span></h3>
    </div>

    <script>
        // Update gesture information every second
        function updateGestureInfo() {
            fetch('/api/current_gesture')
                .then(response => response.json())
                .then(data => {
                    if (data.gesture) {
                        document.getElementById('current-gesture').textContent = data.gesture;
                        document.getElementById('confidence').textContent = `${(data.confidence * 100).toFixed(1)}%`;
                    } else {
                        document.getElementById('current-gesture').textContent = 'None';
                        document.getElementById('confidence').textContent = '0%';
                    }
                });
        }
        
        // Update every second
        setInterval(updateGestureInfo, 1000);
        
        // Initial update
        updateGestureInfo();
    </script>
</body>
</html>
