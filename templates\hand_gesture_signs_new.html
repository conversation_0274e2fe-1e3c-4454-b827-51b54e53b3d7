<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hand Gesture Guide - Hand Gesture Control System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #333;
            color: white;
            padding: 20px 0;
            text-align: center;
        }
        h1 {
            margin: 0;
        }
        .gesture-section {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .gesture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .gesture-card {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        .gesture-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .gesture-card h3 {
            margin-top: 0;
            color: #333;
            text-align: center;
        }
        .gesture-image {
            width: 100%;
            height: 150px;
            background-color: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            border-radius: 5px;
            overflow: hidden;
        }
        .gesture-image img {
            max-width: 100%;
            max-height: 100%;
        }
        .steps {
            margin-top: 15px;
        }
        .steps h4 {
            margin-bottom: 5px;
        }
        .steps ol {
            margin-top: 5px;
            padding-left: 20px;
        }
        .tips {
            margin-top: 15px;
            font-style: italic;
        }
        .nav-buttons {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        .btn {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 0 10px;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #45a049;
        }
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>Hand Gesture Guide</h1>
            <p>Learn how to use the hand gestures to control your computer</p>
        </div>
    </header>

    <div class="container">
        <div class="nav-buttons">
            <a href="/" class="btn">Home</a>
            <a href="/team" class="btn">Team</a>
            <a href="/trained_signs" class="btn">Trained Signs</a>
        </div>

        <div class="gesture-section">
            <h2>Right Hand Gestures</h2>
            <p>Use your right hand for the following gestures to control your mouse and navigation:</p>
            
            <div class="gesture-grid">
                <div class="gesture-card">
                    <div class="gesture-image">
                        <div style="font-size: 60px;">👆</div>
                    </div>
                    <h3>Cursor Movement</h3>
                    <p>Control the mouse cursor by moving your hand with index finger extended.</p>
                    <div class="steps">
                        <h4>How to perform:</h4>
                        <ol>
                            <li>Extend your index finger upward</li>
                            <li>Keep other fingers folded down</li>
                            <li>Move your hand to control the cursor position</li>
                        </ol>
                    </div>
                    <div class="tips">
                        <p>Tip: Keep your hand steady for precise control</p>
                    </div>
                </div>
                
                <div class="gesture-card">
                    <div class="gesture-image">
                        <div style="font-size: 60px;">👌</div>
                    </div>
                    <h3>Left Click</h3>
                    <p>Perform a left mouse click by touching your thumb to your index finger.</p>
                    <div class="steps">
                        <h4>How to perform:</h4>
                        <ol>
                            <li>Extend all fingers</li>
                            <li>Touch your thumb to your index finger</li>
                            <li>Keep other fingers extended</li>
                        </ol>
                    </div>
                    <div class="tips">
                        <p>Tip: Make a clear touching motion</p>
                    </div>
                </div>
                
                <div class="gesture-card">
                    <div class="gesture-image">
                        <div style="font-size: 60px;">🤌</div>
                    </div>
                    <h3>Right Click</h3>
                    <p>Perform a right mouse click by touching your thumb to your middle finger.</p>
                    <div class="steps">
                        <h4>How to perform:</h4>
                        <ol>
                            <li>Extend all fingers</li>
                            <li>Touch your thumb to your middle finger</li>
                            <li>Keep other fingers extended</li>
                        </ol>
                    </div>
                    <div class="tips">
                        <p>Tip: Hold the position briefly for better detection</p>
                    </div>
                </div>
                
                <div class="gesture-card">
                    <div class="gesture-image">
                        <div style="font-size: 60px;">✊</div>
                    </div>
                    <h3>Drag</h3>
                    <p>Click and drag objects by making a fist and moving your hand.</p>
                    <div class="steps">
                        <h4>How to perform:</h4>
                        <ol>
                            <li>Make a fist (close all fingers)</li>
                            <li>Move your hand to drag objects</li>
                            <li>Open your hand to release</li>
                        </ol>
                    </div>
                    <div class="tips">
                        <p>Tip: Close your hand completely for better detection</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="gesture-section">
            <h2>Left Hand Gestures</h2>
            <p>Use your left hand for the following gestures to control system settings:</p>
            
            <div class="gesture-grid">
                <div class="gesture-card">
                    <div class="gesture-image">
                        <div style="font-size: 60px;">🔊</div>
                    </div>
                    <h3>Volume Control</h3>
                    <p>Control system volume with specific finger combinations.</p>
                    <div class="steps">
                        <h4>How to perform:</h4>
                        <ol>
                            <li>Volume Up: Thumb up + Index up (others down)</li>
                            <li>Volume Down: Thumb down + Index up (others down)</li>
                        </ol>
                    </div>
                    <div class="tips">
                        <p>Tip: Make clear thumb positions</p>
                    </div>
                </div>
                
                <div class="gesture-card">
                    <div class="gesture-image">
                        <div style="font-size: 60px;">💡</div>
                    </div>
                    <h3>Brightness Control</h3>
                    <p>Adjust screen brightness with specific finger combinations.</p>
                    <div class="steps">
                        <h4>How to perform:</h4>
                        <ol>
                            <li>Brightness Up: Thumb up + Middle up (others down)</li>
                            <li>Brightness Down: Thumb down + Middle up (others down)</li>
                        </ol>
                    </div>
                    <div class="tips">
                        <p>Tip: Keep your hand in view of the camera</p>
                    </div>
                </div>
                
                <div class="gesture-card">
                    <div class="gesture-image">
                        <div style="font-size: 60px;">🎵</div>
                    </div>
                    <h3>Media Controls</h3>
                    <p>Control media playback with specific gestures.</p>
                    <div class="steps">
                        <h4>How to perform:</h4>
                        <ol>
                            <li>Play/Pause: Peace sign (Index + Middle up, others down)</li>
                            <li>Forward 10s: Thumb + Ring finger up (others down)</li>
                            <li>Backward 10s: Thumb + Little finger up (others down)</li>
                        </ol>
                    </div>
                    <div class="tips">
                        <p>Tip: Hold the gesture briefly for detection</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>&copy; 2023 Hand Gesture Control System</p>
        </div>
    </footer>
</body>
</html>
