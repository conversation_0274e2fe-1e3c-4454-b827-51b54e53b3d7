<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Hand Gesture Control - 3D Theme</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/ai_3d_theme.css') }}">
    <style>
        :root {
            --primary-color: #00ffaa;
            --secondary-color: #6a11cb;
            --dark-color: #121212;
            --light-color: #f0f0f0;
            --accent-color: #ff00aa;
            --gradient-bg: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--dark-color);
            color: var(--light-color);
            overflow-x: hidden;
            min-height: 100vh;
            background: radial-gradient(ellipse at bottom, #1B2735 0%, #090A0F 100%);
        }

        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .star {
            position: absolute;
            background-color: white;
            border-radius: 50%;
            animation: twinkle 5s infinite;
        }

        @keyframes twinkle {
            0% { opacity: 0; }
            50% { opacity: 1; }
            100% { opacity: 0; }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        header {
            text-align: center;
            padding: 30px 0;
            position: relative;
        }

        h1 {
            font-family: 'Orbitron', sans-serif;
            font-size: 3rem;
            margin-bottom: 10px;
            background: linear-gradient(to right, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 0 10px rgba(0, 255, 170, 0.3);
            animation: pulse 3s infinite;
        }

        @keyframes pulse {
            0% { text-shadow: 0 0 10px rgba(0, 255, 170, 0.3); }
            50% { text-shadow: 0 0 20px rgba(0, 255, 170, 0.7); }
            100% { text-shadow: 0 0 10px rgba(0, 255, 170, 0.3); }
        }

        .subtitle {
            font-size: 1.2rem;
            color: #ccc;
            margin-bottom: 20px;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        .video-container {
            position: relative;
            width: 640px;
            height: 480px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 0 30px rgba(0, 255, 170, 0.5);
            transition: all 0.3s ease;
            transform-style: preserve-3d;
            perspective: 1000px;
        }

        .video-container:hover {
            transform: scale(1.02) rotateX(2deg) rotateY(2deg);
            box-shadow: 0 0 40px rgba(0, 255, 170, 0.7);
        }

        .video-container.active {
            box-shadow: 0 0 50px rgba(0, 255, 170, 0.9);
            animation: pulse-container 3s infinite;
        }

        @keyframes pulse-container {
            0% { box-shadow: 0 0 30px rgba(0, 255, 170, 0.7); }
            50% { box-shadow: 0 0 50px rgba(0, 255, 170, 0.9); }
            100% { box-shadow: 0 0 30px rgba(0, 255, 170, 0.7); }
        }

        .video-container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--accent-color));
            z-index: -1;
            border-radius: 17px;
            animation: border-animation 3s linear infinite;
        }

        .video-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                        rgba(0,255,170,0.1) 0%,
                        rgba(0,255,170,0) 50%,
                        rgba(0,255,170,0.1) 100%);
            pointer-events: none;
            z-index: 1;
        }

        @keyframes border-animation {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .video-feed {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 15px;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            background: rgba(0, 0, 0, 0.5);
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            padding: 10px 20px;
            border-radius: 30px;
            font-family: 'Orbitron', sans-serif;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0%;
            height: 100%;
            background: var(--primary-color);
            transition: all 0.3s ease;
            z-index: -1;
        }

        .btn:hover {
            color: var(--dark-color);
        }

        .btn:hover::before {
            width: 100%;
        }

        .gesture-info {
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            width: 100%;
            max-width: 640px;
            border: 1px solid rgba(0, 255, 170, 0.3);
            box-shadow: 0 0 20px rgba(0, 255, 170, 0.2);
        }

        .gesture-title {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .gesture-display {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .gesture-name {
            font-size: 2rem;
            font-weight: bold;
            color: white;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            animation: text-glow 2s infinite;
        }

        @keyframes text-glow {
            0% { text-shadow: 0 0 10px rgba(255, 255, 255, 0.5); }
            50% { text-shadow: 0 0 20px rgba(0, 255, 170, 0.8); }
            100% { text-shadow: 0 0 10px rgba(255, 255, 255, 0.5); }
        }

        .confidence-bar {
            width: 100%;
            height: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            overflow: hidden;
            margin-top: 10px;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(to right, var(--primary-color), var(--accent-color));
            border-radius: 5px;
            transition: width 0.5s ease;
        }

        .confidence-text {
            text-align: right;
            font-size: 0.9rem;
            color: #ccc;
            margin-top: 5px;
        }

        .gesture-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
            width: 100%;
        }

        .gesture-card {
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(0, 255, 170, 0.2);
            transition: all 0.3s ease;
            transform-style: preserve-3d;
            perspective: 1000px;
        }

        .gesture-card:hover {
            transform: translateY(-5px) rotateX(5deg) rotateY(5deg);
            border-color: var(--primary-color);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        .gesture-card h3 {
            font-family: 'Orbitron', sans-serif;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .gesture-card p {
            color: #ccc;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        footer {
            text-align: center;
            padding: 30px 0;
            margin-top: 50px;
            color: #666;
            font-size: 0.9rem;
        }

        /* 3D Hand Model Animation */
        .hand-model {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 100px;
            height: 100px;
            perspective: 1000px;
        }

        .hand {
            width: 100%;
            height: 100%;
            position: relative;
            transform-style: preserve-3d;
            animation: rotate 10s infinite linear;
        }

        @keyframes rotate {
            0% { transform: rotateX(0) rotateY(0); }
            100% { transform: rotateX(360deg) rotateY(360deg); }
        }

        .finger {
            position: absolute;
            background: linear-gradient(var(--primary-color), var(--accent-color));
            border-radius: 5px;
        }

        .thumb {
            width: 20px;
            height: 40px;
            transform: translateX(40px) translateY(20px) translateZ(10px) rotateZ(-30deg);
        }

        .index {
            width: 15px;
            height: 60px;
            transform: translateX(20px) translateY(-10px) translateZ(10px);
        }

        .middle {
            width: 15px;
            height: 65px;
            transform: translateX(0px) translateY(-15px) translateZ(10px);
        }

        .ring {
            width: 15px;
            height: 60px;
            transform: translateX(-20px) translateY(-10px) translateZ(10px);
        }

        .pinky {
            width: 15px;
            height: 50px;
            transform: translateX(-40px) translateY(0px) translateZ(10px);
        }

        .palm {
            width: 80px;
            height: 70px;
            transform: translateY(40px) translateZ(0);
            background: linear-gradient(var(--primary-color), var(--secondary-color));
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="stars" id="stars"></div>

    <div class="container">
        <header>
            <div class="hand-model">
                <div class="hand">
                    <div class="finger thumb"></div>
                    <div class="finger index"></div>
                    <div class="finger middle"></div>
                    <div class="finger ring"></div>
                    <div class="finger pinky"></div>
                    <div class="palm"></div>
                </div>
            </div>
            <h1>AI Hand Gesture Control</h1>
            <p class="subtitle">Control your computer with advanced hand gestures</p>
        </header>

        <div class="main-content">
            <div class="video-container">
                <img src="{{ url_for('video_feed') }}" class="video-feed" alt="Video Feed">
            </div>

            <div class="controls">
                <button class="btn" id="startCamera">Start Camera</button>
                <button class="btn" id="stopCamera">Stop Camera</button>
                <button class="btn" id="gestureMode">Gesture Mode</button>
                <button class="btn" id="signMode">Sign Language</button>
                <button class="btn" id="captureFrame">Capture Frame</button>
                <a href="/team" class="btn btn-3d">Team</a>
            </div>

            <div class="gesture-info">
                <h2 class="gesture-title">Current Detection</h2>
                <div class="gesture-display">
                    <div class="gesture-name" id="current-gesture">No Gesture</div>
                </div>
                <div class="confidence-bar">
                    <div class="confidence-fill" id="confidence-fill" style="width: 0%"></div>
                </div>
                <div class="confidence-text" id="confidence-text">0%</div>
            </div>

            <div class="gesture-cards">
                <div class="gesture-card">
                    <h3>Thumb Up</h3>
                    <p>Extend your thumb upward while keeping all other fingers closed.</p>
                </div>
                <div class="gesture-card">
                    <h3>Peace Sign</h3>
                    <p>Extend your index and middle fingers while keeping other fingers closed.</p>
                </div>
                <div class="gesture-card">
                    <h3>Open Palm</h3>
                    <p>Extend all fingers with palm facing forward.</p>
                </div>
                <div class="gesture-card">
                    <h3>Fist</h3>
                    <p>Close all fingers to form a fist.</p>
                </div>
                <div class="gesture-card">
                    <h3>OK Sign</h3>
                    <p>Form a circle with your thumb and index finger, extending other fingers.</p>
                </div>
                <div class="gesture-card">
                    <h3>Volume Control</h3>
                    <p>Use thumb up/down with index finger to control system volume.</p>
                </div>
            </div>
        </div>

        <footer>
            <p>© 2025 AI Hand Gesture Control System | Advanced Computer Vision Technology</p>
        </footer>
    </div>

    <script src="{{ url_for('static', filename='js/ai_3d_effects.js') }}"></script>
    <script>
        // Global variables
        let cameraActive = false;
        let updateInterval = null;

        // Update gesture information
        function updateGestureInfo() {
            fetch('/api/current_gesture')
                .then(response => response.json())
                .then(data => {
                    if (data.gesture) {
                        document.getElementById('current-gesture').textContent = data.gesture;
                        document.getElementById('confidence-fill').style.width = `${data.confidence * 100}%`;
                        document.getElementById('confidence-text').textContent = `${(data.confidence * 100).toFixed(1)}%`;
                    } else {
                        document.getElementById('current-gesture').textContent = 'No Gesture';
                        document.getElementById('confidence-fill').style.width = '0%';
                        document.getElementById('confidence-text').textContent = '0%';
                    }
                })
                .catch(error => {
                    console.error('Error fetching gesture info:', error);
                });
        }

        // Set detection mode
        function setMode(mode) {
            fetch('/api/mode', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mode: mode }),
            })
                .then(response => response.json())
                .then(data => {
                    console.log('Mode set to:', data.mode);
                    // Show visual feedback
                    showNotification(`Mode set to: ${data.mode}`);
                })
                .catch(error => {
                    console.error('Error setting mode:', error);
                    showNotification('Error setting mode', 'error');
                });
        }

        // Start camera
        function startCamera() {
            showNotification('Starting camera...', 'info');

            fetch('/start_camera')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        cameraActive = true;
                        document.getElementById('startCamera').classList.add('active');
                        document.getElementById('stopCamera').classList.remove('active');
                        showNotification('Camera started successfully');

                        // Refresh the video feed
                        const videoFeed = document.querySelector('.video-feed');
                        const currentSrc = videoFeed.src;
                        videoFeed.src = currentSrc + '?' + new Date().getTime();

                        // Start updating gesture info
                        if (updateInterval) clearInterval(updateInterval);
                        updateInterval = setInterval(updateGestureInfo, 500);

                        // Add active class to video container
                        document.querySelector('.video-container').classList.add('active');
                    } else {
                        showNotification('Failed to start camera: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Error starting camera:', error);
                    showNotification('Error starting camera', 'error');
                });
        }

        // Stop camera
        function stopCamera() {
            showNotification('Stopping camera...', 'info');

            fetch('/stop_camera')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        cameraActive = false;
                        document.getElementById('stopCamera').classList.add('active');
                        document.getElementById('startCamera').classList.remove('active');
                        showNotification('Camera stopped successfully');

                        // Stop updating gesture info
                        if (updateInterval) {
                            clearInterval(updateInterval);
                            updateInterval = null;
                        }

                        // Remove active class from video container
                        document.querySelector('.video-container').classList.remove('active');

                        // Refresh the video feed to show demo mode
                        const videoFeed = document.querySelector('.video-feed');
                        const currentSrc = videoFeed.src;
                        videoFeed.src = currentSrc + '?' + new Date().getTime();
                    } else {
                        showNotification('Failed to stop camera: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Error stopping camera:', error);
                    showNotification('Error stopping camera', 'error');
                });
        }

        // Capture frame
        function captureFrame() {
            if (!cameraActive) {
                showNotification('Camera is not active. Starting demo capture.', 'info');
            } else {
                showNotification('Capturing frame...', 'info');
            }

            fetch('/capture_frame')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Frame captured successfully!');

                        // Show captured image in a modal
                        showCapturedImage(data.filepath);
                    } else {
                        showNotification('Failed to capture frame: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Error capturing frame:', error);
                    showNotification('Error capturing frame', 'error');
                });
        }

        // Show captured image in a modal
        function showCapturedImage(filepath) {
            // Create modal if it doesn't exist
            let modal = document.getElementById('captureModal');
            if (!modal) {
                modal = document.createElement('div');
                modal.id = 'captureModal';
                modal.style.position = 'fixed';
                modal.style.top = '0';
                modal.style.left = '0';
                modal.style.width = '100%';
                modal.style.height = '100%';
                modal.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                modal.style.display = 'flex';
                modal.style.alignItems = 'center';
                modal.style.justifyContent = 'center';
                modal.style.zIndex = '2000';
                modal.style.opacity = '0';
                modal.style.transition = 'opacity 0.3s';

                // Close button
                const closeBtn = document.createElement('button');
                closeBtn.textContent = '×';
                closeBtn.style.position = 'absolute';
                closeBtn.style.top = '20px';
                closeBtn.style.right = '20px';
                closeBtn.style.background = 'none';
                closeBtn.style.border = 'none';
                closeBtn.style.color = 'white';
                closeBtn.style.fontSize = '30px';
                closeBtn.style.cursor = 'pointer';
                closeBtn.onclick = function() {
                    modal.style.opacity = '0';
                    setTimeout(() => {
                        modal.style.display = 'none';
                    }, 300);
                };

                modal.appendChild(closeBtn);
                document.body.appendChild(modal);
            }

            // Clear previous content
            modal.innerHTML = '';

            // Add close button
            const closeBtn = document.createElement('button');
            closeBtn.textContent = '×';
            closeBtn.style.position = 'absolute';
            closeBtn.style.top = '20px';
            closeBtn.style.right = '20px';
            closeBtn.style.background = 'none';
            closeBtn.style.border = 'none';
            closeBtn.style.color = 'white';
            closeBtn.style.fontSize = '30px';
            closeBtn.style.cursor = 'pointer';
            closeBtn.onclick = function() {
                modal.style.opacity = '0';
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 300);
            };

            // Add image
            const img = document.createElement('img');
            img.src = filepath;
            img.style.maxWidth = '80%';
            img.style.maxHeight = '80%';
            img.style.border = '3px solid var(--primary-color)';
            img.style.boxShadow = '0 0 30px rgba(0, 255, 170, 0.5)';

            modal.appendChild(closeBtn);
            modal.appendChild(img);

            // Show modal
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.style.opacity = '1';
            }, 10);
        }

        // Show notification
        function showNotification(message, type = 'success') {
            // Create notification element if it doesn't exist
            let notification = document.getElementById('notification');
            if (!notification) {
                notification = document.createElement('div');
                notification.id = 'notification';
                notification.style.position = 'fixed';
                notification.style.top = '20px';
                notification.style.right = '20px';
                notification.style.padding = '15px 20px';
                notification.style.borderRadius = '5px';
                notification.style.color = 'white';
                notification.style.fontFamily = 'Orbitron, sans-serif';
                notification.style.zIndex = '1000';
                notification.style.opacity = '0';
                notification.style.transform = 'translateY(-20px)';
                notification.style.transition = 'opacity 0.3s, transform 0.3s';
                document.body.appendChild(notification);
            }

            // Set notification style based on type
            if (type === 'success') {
                notification.style.backgroundColor = 'rgba(0, 255, 170, 0.8)';
                notification.style.boxShadow = '0 0 15px rgba(0, 255, 170, 0.5)';
            } else if (type === 'info') {
                notification.style.backgroundColor = 'rgba(0, 170, 255, 0.8)';
                notification.style.boxShadow = '0 0 15px rgba(0, 170, 255, 0.5)';
            } else {
                notification.style.backgroundColor = 'rgba(255, 0, 0, 0.8)';
                notification.style.boxShadow = '0 0 15px rgba(255, 0, 0, 0.5)';
            }

            // Set message and show notification
            notification.textContent = message;
            notification.style.opacity = '1';
            notification.style.transform = 'translateY(0)';

            // Hide notification after 3 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateY(-20px)';
            }, 3000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Create 3D particles
            createParticles();

            // Camera control buttons
            document.getElementById('startCamera').addEventListener('click', startCamera);
            document.getElementById('stopCamera').addEventListener('click', stopCamera);

            // Set mode buttons
            document.getElementById('gestureMode').addEventListener('click', function() {
                setMode('gesture');
                this.style.borderColor = '#ff00aa';
                document.getElementById('signMode').style.borderColor = '#00ffaa';
            });

            document.getElementById('signMode').addEventListener('click', function() {
                setMode('sign');
                this.style.borderColor = '#ff00aa';
                document.getElementById('gestureMode').style.borderColor = '#00ffaa';
            });

            // Capture frame
            document.getElementById('captureFrame').addEventListener('click', captureFrame);

            // Initial setup
            document.getElementById('stopCamera').classList.add('active');

            // Start updating gesture info for demo mode
            updateInterval = setInterval(updateGestureInfo, 1000);

            // Add parallax effect to elements
            document.addEventListener('mousemove', function(e) {
                const mouseX = e.clientX / window.innerWidth;
                const mouseY = e.clientY / window.innerHeight;

                const offsetX = (mouseX - 0.5) * 20;
                const offsetY = (mouseY - 0.5) * 20;

                // Apply to heading
                const heading = document.querySelector('h1');
                if (heading) {
                    heading.style.transform = `translateX(${offsetX * 0.5}px) translateY(${offsetY * 0.5}px)`;
                }

                // Apply to video container
                const videoContainer = document.querySelector('.video-container');
                if (videoContainer) {
                    videoContainer.style.transform = `translateX(${offsetX * 0.2}px) translateY(${offsetY * 0.2}px) scale(1.02)`;
                }

                // Apply to gesture cards
                const cards = document.querySelectorAll('.gesture-card');
                cards.forEach((card, index) => {
                    const intensity = 0.2 + (index * 0.05);
                    card.style.transform = `translateX(${offsetX * intensity}px) translateY(${offsetY * intensity}px) rotateX(${-offsetY}deg) rotateY(${offsetX}deg)`;
                });
            });
        });

        // Create floating particles
        function createParticles() {
            // Create container if it doesn't exist
            let container = document.querySelector('.particle-container');
            if (!container) {
                container = document.createElement('div');
                container.className = 'particle-container';
                container.style.position = 'fixed';
                container.style.top = '0';
                container.style.left = '0';
                container.style.width = '100%';
                container.style.height = '100%';
                container.style.pointerEvents = 'none';
                container.style.zIndex = '-1';
                document.body.appendChild(container);
            }

            // Number of particles
            const particleCount = 30;

            // Create particles
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size between 5px and 20px
                const size = Math.random() * 15 + 5;

                // Random position
                const posX = Math.random() * 100;
                const posY = Math.random() * 100;

                // Random opacity
                const opacity = Math.random() * 0.5 + 0.1;

                // Set styles
                particle.style.position = 'absolute';
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                particle.style.left = `${posX}%`;
                particle.style.top = `${posY}%`;
                particle.style.opacity = opacity;
                particle.style.background = 'radial-gradient(circle, rgba(0,255,170,0.8) 0%, rgba(0,255,170,0) 70%)';
                particle.style.borderRadius = '50%';

                // Add animation with random duration and delay
                const duration = Math.random() * 10 + 10; // 10-20s
                const delay = Math.random() * 5; // 0-5s

                particle.style.animation = `float ${duration}s ease-in-out ${delay}s infinite`;

                // Add to container
                container.appendChild(particle);
            }
        }
    </script>
</body>
</html>
